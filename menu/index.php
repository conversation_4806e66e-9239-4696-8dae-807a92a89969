<?php
// Champions Sports Bar - Menu Page
require_once '../config.php';

$page_title = "Menu - Champions Sports Bar & Grill";
$page_description = "Explore our delicious menu featuring appetizers, burgers, sandwiches, entrees, and more. Great food and drinks at Champions Sports Bar in Brownstown, MI.";
$current_page = "menu";

// Get database connection
$db = getDB();

// Get all active menu categories
$categories = $db->fetchAll("
    SELECT * FROM menu_categories
    WHERE is_active = 1
    ORDER BY sort_order ASC, name ASC
");

// Get all active menu items with category information
$menuItems = $db->fetchAll("
    SELECT mi.*, mc.name as category_name, mc.sort_order as category_sort_order
    FROM menu_items mi
    JOIN menu_categories mc ON mi.category_id = mc.id
    WHERE mi.is_available = 1 AND mc.is_active = 1
    ORDER BY mc.sort_order ASC, mi.is_featured DESC, mi.name ASC
");

// Group menu items by category
$itemsByCategory = [];
foreach ($menuItems as $item) {
    $categoryName = $item['category_name'];
    if (!isset($itemsByCategory[$categoryName])) {
        $itemsByCategory[$categoryName] = [];
    }
    $itemsByCategory[$categoryName][] = $item;
}

include '../includes/header.php';
?>

<!-- Page Header -->
<section class="page-header bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-4 fw-bold mb-3">Our Menu</h1>
                <p class="lead">Delicious food and refreshing drinks in a sports bar atmosphere</p>
            </div>
        </div>
    </div>
</section>

<!-- Menu Navigation -->
<section class="menu-nav py-4 bg-light sticky-top">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex flex-wrap justify-content-center gap-2">
                    <button class="btn btn-outline-primary menu-filter-btn active" data-filter="all">All Items</button>
                    <?php foreach ($categories as $category):
                        $categorySlug = strtolower(str_replace([' ', '&'], ['-', 'and'], $category['name']));
                    ?>
                        <button class="btn btn-outline-primary menu-filter-btn" data-filter="<?php echo $categorySlug; ?>">
                            <?php echo htmlspecialchars($category['name']); ?>
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Menu Content -->
<section class="menu-content py-5">
    <div class="container">

        <?php if (empty($itemsByCategory)): ?>
            <div class="text-center py-5">
                <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                <h3>Menu Coming Soon</h3>
                <p class="text-muted">We're updating our menu. Please check back soon!</p>
            </div>
        <?php else: ?>

            <?php foreach ($categories as $category):
                $categoryName = $category['name'];
                $categorySlug = strtolower(str_replace([' ', '&'], ['-', 'and'], $categoryName));

                if (!isset($itemsByCategory[$categoryName]) || empty($itemsByCategory[$categoryName])) {
                    continue;
                }

                $items = $itemsByCategory[$categoryName];
            ?>
                <!-- <?php echo htmlspecialchars($categoryName); ?> -->
                <div class="menu-category mb-5" id="<?php echo $categorySlug; ?>" data-category="<?php echo $categorySlug; ?>">
                    <div class="d-flex align-items-center mb-4">
                        <h2 class="display-6 fw-bold text-primary mb-0"><?php echo htmlspecialchars($categoryName); ?></h2>
                        <?php if ($category['description']): ?>
                            <small class="text-muted ms-3"><?php echo htmlspecialchars($category['description']); ?></small>
                        <?php endif; ?>
                    </div>

                    <div class="row">
                        <?php foreach ($items as $index => $item): ?>
                            <div class="col-lg-6 mb-4">
                                <div class="menu-item h-100" data-category="<?php echo $categorySlug; ?>">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <?php if ($item['image_url']): ?>
                                            <div class="menu-item-image">
                                                <img src="/<?php echo htmlspecialchars($item['image_url']); ?>"
                                                     alt="<?php echo htmlspecialchars($item['name']); ?>"
                                                     class="card-img-top" style="height: 200px; object-fit: cover;">
                                                <?php if ($item['is_featured']): ?>
                                                    <div class="position-absolute top-0 end-0 m-2">
                                                        <span class="badge bg-warning text-dark">
                                                            <i class="fas fa-star me-1"></i>Featured
                                                        </span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>

                                        <div class="card-body">
                                            <div class="menu-item-header d-flex justify-content-between align-items-start mb-2">
                                                <h5 class="card-title mb-0">
                                                    <?php echo htmlspecialchars($item['name']); ?>
                                                    <?php if ($item['is_featured'] && !$item['image_url']): ?>
                                                        <span class="badge bg-warning text-dark ms-2">
                                                            <i class="fas fa-star"></i>
                                                        </span>
                                                    <?php endif; ?>
                                                </h5>
                                                <span class="price fw-bold text-primary fs-5">
                                                    $<?php echo number_format($item['price'], 2); ?>
                                                </span>
                                            </div>

                                            <?php if ($item['description']): ?>
                                                <p class="card-text text-muted mb-3">
                                                    <?php echo htmlspecialchars($item['description']); ?>
                                                </p>
                                            <?php endif; ?>

                                            <div class="menu-item-details">
                                                <div class="d-flex flex-wrap gap-2 align-items-center">
                                                    <?php if ($item['calories']): ?>
                                                        <small class="badge bg-light text-dark">
                                                            <i class="fas fa-fire me-1"></i><?php echo $item['calories']; ?> cal
                                                        </small>
                                                    <?php endif; ?>

                                                    <?php if ($item['spice_level']): ?>
                                                        <small class="badge bg-danger">
                                                            <?php echo str_repeat('🌶️', $item['spice_level']); ?>
                                                        </small>
                                                    <?php endif; ?>

                                                    <?php if ($item['allergens']): ?>
                                                        <small class="badge bg-warning text-dark" title="<?php echo htmlspecialchars($item['allergens']); ?>">
                                                            <i class="fas fa-exclamation-triangle me-1"></i>Allergens
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <?php if (($index + 1) % 2 === 0): ?>
                                </div><div class="row">
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>

        <?php endif; ?>
    </div>
</section>

<!-- Menu Search and Filter -->
<section class="menu-search py-4 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" id="menuSearch" placeholder="Search menu items...">
                    <button class="btn btn-outline-primary" type="button" onclick="searchMenu()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Menu CTA -->
<section class="menu-cta py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3 class="h2 mb-3">Hungry? Come Visit Us!</h3>
                <p class="lead mb-0">Experience our delicious food and great atmosphere at Champions Sports Bar & Grill.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="../contact/" class="btn btn-light btn-lg me-3">Get Directions</a>
                <a href="tel:+17342847000" class="btn btn-outline-light btn-lg">Call Now</a>
            </div>
        </div>
    </div>
</section>
<script>
// Menu filtering functionality
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.menu-filter-btn');
    const menuItems = document.querySelectorAll('.menu-item');
    const menuCategories = document.querySelectorAll('.menu-category');

    // Filter menu function
    function filterMenu(filter) {
        // Update active button
        filterButtons.forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

        if (filter === 'all') {
            // Show all categories and items
            menuCategories.forEach(category => {
                category.style.display = 'block';
            });
            menuItems.forEach(item => {
                item.style.display = 'block';
            });
        } else {
            // Hide all categories first
            menuCategories.forEach(category => {
                category.style.display = 'none';
            });

            // Show only the selected category
            const targetCategory = document.querySelector(`[data-category="${filter}"]`);
            if (targetCategory) {
                targetCategory.style.display = 'block';
            }
        }

        // Smooth scroll to top of menu content
        document.querySelector('.menu-content').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }

    // Search menu function
    window.searchMenu = function() {
        const searchTerm = document.getElementById('menuSearch').value.toLowerCase();
        const menuItems = document.querySelectorAll('.menu-item');
        const menuCategories = document.querySelectorAll('.menu-category');

        if (searchTerm === '') {
            // Show all items if search is empty
            menuCategories.forEach(category => {
                category.style.display = 'block';
            });
            menuItems.forEach(item => {
                item.style.display = 'block';
            });
            return;
        }

        // Hide all categories first
        menuCategories.forEach(category => {
            category.style.display = 'none';
        });

        let hasResults = false;

        menuItems.forEach(item => {
            const itemName = item.querySelector('.card-title')?.textContent.toLowerCase() || '';
            const itemDescription = item.querySelector('.card-text')?.textContent.toLowerCase() || '';

            if (itemName.includes(searchTerm) || itemDescription.includes(searchTerm)) {
                item.style.display = 'block';
                // Show the parent category
                const parentCategory = item.closest('.menu-category');
                if (parentCategory) {
                    parentCategory.style.display = 'block';
                }
                hasResults = true;
            } else {
                item.style.display = 'none';
            }
        });

        if (!hasResults) {
            // Show "no results" message
            console.log('No menu items found for: ' + searchTerm);
        }
    };

    // Add event listeners to filter buttons
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.dataset.filter;
            filterMenu(filter);
        });
    });

    // Add enter key support for search
    document.getElementById('menuSearch')?.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchMenu();
        }
    });
});
</script>

<?php include '../includes/footer.php'; ?>
